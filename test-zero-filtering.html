<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Zero Filtering</title>
    <link rel="stylesheet" href="components/data-grid/snap-grid.css">
</head>
<body>
    <h1>Test Zero Filtering in Numeric Columns</h1>
    <div id="test-grid"></div>

    <script src="components/data-grid/snap-grid.js"></script>
    <script>
        // Test data with zero values
        const testData = [
            { id: 1, product: 'T-Shirt A', sales: 0, price: 19.99 },
            { id: 2, product: 'T-Shirt B', sales: 5, price: 24.99 },
            { id: 3, product: 'T-Shirt C', sales: 0, price: 0 },
            { id: 4, product: 'T-Shirt D', sales: 10, price: 29.99 },
            { id: 5, product: 'T-Shirt E', sales: 0, price: 15.99 }
        ];

        const config = {
            data: testData,
            columns: [
                { field: 'id', headerName: 'ID', type: 'number', width: 80 },
                { field: 'product', headerName: 'Product', type: 'text', width: 150 },
                { field: 'sales', headerName: 'Sales', type: 'number', width: 100 },
                { field: 'price', headerName: 'Price', type: 'number', width: 100 }
            ],
            height: 400,
            filterable: true
        };

        const grid = new SnapGrid(document.getElementById('test-grid'), config);

        // Test instructions
        console.log('Test Instructions:');
        console.log('1. Click on the Sales column filter');
        console.log('2. Select "Equals" operator');
        console.log('3. Enter "0" in the filter input');
        console.log('4. Apply the filter');
        console.log('5. You should see only products with 0 sales (T-Shirt A, T-Shirt C, T-Shirt E)');
        console.log('');
        console.log('Test data:', testData);

        // Test the numeric filter function directly
        console.log('');
        console.log('🧪 Direct function test:');
        const testValue = 0;
        const testOperator = 'equals';
        const testFilterValue = '0';

        // Simulate the toNum function from applyNumberFilter
        const toNum = (v) => {
            if (v === null || v === undefined || v === '') return NaN;
            if (typeof v === 'number') return v;
            if (typeof v === 'string') {
                const cleaned = v.replace(/[^0-9.-]/g, '');
                return cleaned === '' || cleaned === '-' || cleaned === '.' ? NaN : Number(cleaned);
            }
            return Number(v);
        };

        const num = toNum(testValue);
        const filter = toNum(testFilterValue);
        const result = num === filter;

        console.log('Direct test result:', {
            testValue,
            testFilterValue,
            parsedNum: num,
            parsedFilter: filter,
            equalsResult: result
        });
    </script>
</body>
</html>
